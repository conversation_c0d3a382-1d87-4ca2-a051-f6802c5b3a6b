#!/usr/bin/env python3
"""
Test script to verify the stop test case functionality works correctly.
This script tests the implementation without requiring a full app setup.
"""

import sys
import os
import json
import tempfile
import shutil
from unittest.mock import Mock, patch

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app_android'))

def test_stop_test_case_method():
    """Test the stop_test_case method in the Player class"""
    print("Testing stop_test_case method...")
    
    try:
        # Import the Player class
        from app.utils.player import Player
        
        # Create a mock device controller
        mock_device_controller = Mock()
        
        # Create a temporary test cases directory
        temp_dir = tempfile.mkdtemp()
        
        try:
            # Create a Player instance
            player = Player(device_controller=mock_device_controller, test_cases_dir=temp_dir)
            
            # Test stopping a test case
            result = player.stop_test_case(0, "Test Case 1")
            
            # Verify the result
            assert result == True, "stop_test_case should return True"
            assert hasattr(player, 'skipped_test_cases'), "Player should have skipped_test_cases attribute"
            assert 0 in player.skipped_test_cases, "Test case 0 should be in skipped_test_cases"
            
            print("✓ stop_test_case method works correctly")
            
            # Test stopping multiple test cases
            result2 = player.stop_test_case(2, "Test Case 3")
            assert result2 == True, "stop_test_case should return True for second call"
            assert 2 in player.skipped_test_cases, "Test case 2 should be in skipped_test_cases"
            assert len(player.skipped_test_cases) == 2, "Should have 2 skipped test cases"
            
            print("✓ Multiple test case stopping works correctly")
            
        finally:
            # Clean up temporary directory
            shutil.rmtree(temp_dir)
            
    except ImportError as e:
        print(f"✗ Could not import Player class: {e}")
        return False
    except Exception as e:
        print(f"✗ Error testing stop_test_case method: {e}")
        return False
    
    return True

def test_css_styles():
    """Test that CSS styles for skipped state exist"""
    print("Testing CSS styles...")
    
    try:
        css_file = os.path.join(os.path.dirname(__file__), 'app', 'static', 'css', 'style.css')
        
        if not os.path.exists(css_file):
            print(f"✗ CSS file not found: {css_file}")
            return False
            
        with open(css_file, 'r') as f:
            css_content = f.read()
            
        # Check for skipped styles
        required_styles = [
            '.action-item.skipped',
            '.test-case-header.skipped',
            'border-left: 4px solid #6c757d'
        ]
        
        for style in required_styles:
            if style not in css_content:
                print(f"✗ Missing CSS style: {style}")
                return False
                
        print("✓ CSS styles for skipped state exist")
        return True
        
    except Exception as e:
        print(f"✗ Error checking CSS styles: {e}")
        return False

def test_javascript_functions():
    """Test that JavaScript functions for stop buttons exist"""
    print("Testing JavaScript functions...")
    
    try:
        js_files = [
            os.path.join(os.path.dirname(__file__), 'app', 'static', 'js', 'main.js'),
            os.path.join(os.path.dirname(__file__), 'app_android', 'static', 'js', 'main.js')
        ]
        
        for js_file in js_files:
            if not os.path.exists(js_file):
                print(f"✗ JavaScript file not found: {js_file}")
                return False
                
            with open(js_file, 'r') as f:
                js_content = f.read()
                
            # Check for required functions and elements
            required_elements = [
                'stop-test-case',
                'showStopButtons',
                'hideStopButtons',
                'Stop this test case execution',
                '/api/action/stop_test_case'
            ]
            
            for element in required_elements:
                if element not in js_content:
                    print(f"✗ Missing JavaScript element in {js_file}: {element}")
                    return False
                    
        print("✓ JavaScript functions for stop buttons exist")
        return True
        
    except Exception as e:
        print(f"✗ Error checking JavaScript functions: {e}")
        return False

def test_api_endpoints():
    """Test that API endpoints exist in app.py files"""
    print("Testing API endpoints...")
    
    try:
        app_files = [
            os.path.join(os.path.dirname(__file__), 'app', 'app.py'),
            os.path.join(os.path.dirname(__file__), 'app_android', 'app.py')
        ]
        
        for app_file in app_files:
            if not os.path.exists(app_file):
                print(f"✗ App file not found: {app_file}")
                return False
                
            with open(app_file, 'r') as f:
                app_content = f.read()
                
            # Check for the stop_test_case endpoint
            if '/api/action/stop_test_case' not in app_content:
                print(f"✗ Missing API endpoint in {app_file}: /api/action/stop_test_case")
                return False
                
            if 'def stop_test_case():' not in app_content:
                print(f"✗ Missing function in {app_file}: stop_test_case")
                return False
                
        print("✓ API endpoints for stop test case exist")
        return True
        
    except Exception as e:
        print(f"✗ Error checking API endpoints: {e}")
        return False

def main():
    """Run all tests"""
    print("=" * 60)
    print("Testing Stop Test Case Functionality Implementation")
    print("=" * 60)
    
    tests = [
        test_stop_test_case_method,
        test_css_styles,
        test_javascript_functions,
        test_api_endpoints
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        print()
        if test():
            passed += 1
        else:
            print("Test failed!")
    
    print()
    print("=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All tests passed! Stop test case functionality is properly implemented.")
        return 0
    else:
        print("✗ Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
